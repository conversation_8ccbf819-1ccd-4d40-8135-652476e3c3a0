import { Link } from "@tanstack/react-router";
import { Clock, FileText } from "lucide-react";
import { useState } from "react";
import { toast } from "react-toastify";
import { useAppForm } from "~/core/components/form/form";
import { getErrorResult } from "~/core/utils/effectErrors";
import useEditSchedule from "../../hooks/use-edit-schedule";
import type { Schedule } from "../../service/model/schedule";
import { CreateScheduleSchema } from "../../utils/schema";
import SchedulePreview from "../SchedulePreview";
import TurnsTable from "../TurnsTable";

interface EditScheduleFormProps {
	id: string;
	schedule: Schedule;
}
export default function EditSchedule({ id, schedule }: EditScheduleFormProps) {
	const { mutate } = useEditSchedule();
	const [selectedTurnIndex, setSelectedTurnIndex] = useState<number | null>(
		null,
	);

	const form = useAppForm({
		defaultValues: {
			...schedule,
		} as CreateScheduleSchema,
		validators: {
			onChange: CreateScheduleSchema,
		},
		onSubmit: ({ value }) => {
			mutate(
				{
					id,
					name: value.name,
					sessionDuration: value.sessionDuration,
					breakDuration: value.breakDuration,
					turns: value.turns.map((turn, index) => ({
						id: schedule?.turns[index]?.id || "",
						name: turn.name,
						startTime: turn.startTime,
						endTime: turn.endTime,
					})),
				},
				{
					onSuccess: () => {
						toast.success("Horario actualizado exitosamente");
						window.history.back();
					},
					onError: (_error) => {
						console.log(_error);
						const { error } = getErrorResult(_error);
						toast.error(error.message);
					},
				},
			);
		},
	});

	const turns = form.state.values.turns || [];
	const selectedTurn =
		selectedTurnIndex !== null ? turns[selectedTurnIndex] : null;

	return (
		<form
			onSubmit={(e) => {
				e.preventDefault();
				form.handleSubmit();
			}}
		>
			<form.AppForm>
				<div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
					{/* Left Column - Form */}
					<div className="space-y-6">
						<fieldset className="fieldset">
							<form.AppField
								name="name"
								children={({ FSTextField }) => (
									<FSTextField
										label="Nombre del Horario"
										placeholder="Nombre del Horario"
										prefixComponent={<FileText size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="sessionDuration"
								children={({ FSTextField }) => (
									<FSTextField
										label="Duración de Sesión (minutos)"
										placeholder="60"
										type="number"
										prefixComponent={<Clock size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="breakDuration"
								children={({ FSTextField }) => (
									<FSTextField
										label="Duración de Descanso (minutos)"
										placeholder="15"
										type="number"
										prefixComponent={<Clock size={16} />}
									/>
								)}
							/>
						</fieldset>

						<TurnsTable
							form={form}
							selectedTurnIndex={selectedTurnIndex}
							onTurnSelect={setSelectedTurnIndex}
						/>

						<div className="flex gap-4">
							<button type="submit" className="btn btn-primary">
								Actualizar Horario
							</button>
							<Link to="/admin/schedules" className="btn btn-outline">
								Cancelar
							</Link>
						</div>
					</div>

					{/* Right Column - Schedule Preview */}
					<div className="space-y-6">
						<h3 className="font-semibold text-xl">Vista Previa del Horario</h3>
						{selectedTurn ? (
							<SchedulePreview
								turn={selectedTurn}
								sessionDuration={form.state.values.sessionDuration || 60}
								breakDuration={form.state.values.breakDuration || 15}
							/>
						) : (
							<div className="card bg-base-200">
								<div className="card-body text-center">
									<p className="text-base-content/70">
										Selecciona un turno para ver la vista previa del horario
									</p>
								</div>
							</div>
						)}
					</div>
				</div>
			</form.AppForm>
		</form>
	);
}
